import fs from "fs";
import https from "https";
import path from "path";

/**
 * 修复 Git LFS 文件问题
 * 下载真实的数据文件来替换 Git LFS 指针
 */

// 检查文件是否是 Git LFS 指针
function isGitLfsPointer(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  const content = fs.readFileSync(filePath, "utf8");
  return content.startsWith("version https://git-lfs.github.com/spec/v1");
}

// 下载文件
function downloadFile(url, outputPath) {
  return new Promise((resolve, reject) => {
    console.log(`正在下载: ${url}`);
    console.log(`保存到: ${outputPath}`);

    const file = fs.createWriteStream(outputPath);

    https
      .get(url, (response) => {
        if (response.statusCode === 200) {
          response.pipe(file);
          file.on("finish", () => {
            file.close();
            console.log(`下载完成: ${outputPath}`);
            resolve(outputPath);
          });
        } else if (response.statusCode === 302 || response.statusCode === 301) {
          // 处理重定向
          const redirectUrl = response.headers.location;
          console.log(`重定向到: ${redirectUrl}`);
          file.close();
          fs.unlinkSync(outputPath); // 删除空文件
          downloadFile(redirectUrl, outputPath).then(resolve).catch(reject);
        } else {
          file.close();
          fs.unlinkSync(outputPath); // 删除空文件
          reject(new Error(`下载失败: HTTP ${response.statusCode}`));
        }
      })
      .on("error", (err) => {
        file.close();
        if (fs.existsSync(outputPath)) {
          fs.unlinkSync(outputPath); // 删除空文件
        }
        reject(err);
      });
  });
}

// 数据源配置
const dataSources = [
  {
    name: "California ZIP Codes",
    file: "data/california-zip-codes.geojson",
    url: "https://raw.githubusercontent.com/OpenDataDE/State-zip-code-GeoJSON/master/ca_california_zip_codes_geo.min.json",
  },
];

async function fixLfsFiles() {
  console.log("检查和修复 Git LFS 文件...\n");

  for (const source of dataSources) {
    const filePath = path.join(__dirname, "..", source.file);

    console.log(`检查文件: ${source.file}`);

    if (isGitLfsPointer(filePath)) {
      console.log(`发现 Git LFS 指针文件: ${source.file}`);
      console.log(`正在下载真实数据...`);

      try {
        // 备份原文件
        const backupPath = filePath + ".lfs-backup";
        fs.copyFileSync(filePath, backupPath);
        console.log(`已备份原文件到: ${backupPath}`);

        // 下载真实数据
        await downloadFile(source.url, filePath);

        // 验证下载的文件
        const content = fs.readFileSync(filePath, "utf8");
        if (content.startsWith("{") || content.startsWith("[")) {
          console.log(`✅ ${source.name} 下载成功并验证通过`);
        } else {
          throw new Error("下载的文件不是有效的 JSON");
        }
      } catch (error) {
        console.error(`❌ 下载 ${source.name} 失败:`, error.message);

        // 恢复备份文件
        const backupPath = filePath + ".lfs-backup";
        if (fs.existsSync(backupPath)) {
          fs.copyFileSync(backupPath, filePath);
          console.log(`已恢复原文件`);
        }
      }
    } else {
      console.log(`✅ ${source.file} 不是 Git LFS 指针文件，无需处理`);
    }

    console.log(""); // 空行分隔
  }

  console.log("Git LFS 文件检查完成！");
}

// 运行修复
fixLfsFiles().catch(console.error);
